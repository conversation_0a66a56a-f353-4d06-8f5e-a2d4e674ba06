<?php

namespace App\Http\Controllers\Lookups;

use App\Http\Controllers\ApiBaseController;

use App\Models\Category;
use App\Repositories\Category\CategoryRepositoryInterface;
use App\Traits\Models\HasFilters;
use App\Helper\CacheHelper;
use App\Http\Resources\OptionsResource;


use Illuminate\Http\JsonResponse;

class CategoryLookupController extends ApiBaseController
{
    /**
     * @param  CategoryRepositoryInterface $categoryRepository
     * @param  Category $model
     * @param  CacheHelper $cacheHelper
     */
    public function __construct(
        protected CategoryRepositoryInterface $categoryRepository,
        protected Category $model,
        protected CacheHelper $cacheHelper,
        public int $minutes = 100
    ) {
    }
    /**
     * Get all Addresses.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {


        $keyCache = request()->path() . "/" . request()->getQueryString();

        $with = [
            'media',
            'parent.media',
            'children',
            'children.media',
            'children.children',
            'children.children.media',
            'children.children.children',
            'children.children.children.media',
        ];


        $modelOptions = $this->model->getLookupResourceConfig();

        if (has_trait($this->model, HasFilters::class)) {

            if ($count = $this->model->lookupMaxRecords()) {

                $data = $this->cacheHelper->remember(
                    key: $keyCache,
                    callback: function () use ($count, $with) {
                        return $this->categoryRepository->getAllFilteredTake($count, $with);
                    },
                    tags: [],
                    ttl: $this->minutes,
                );

            } else {

                $data = $this->cacheHelper->remember(
                    key: $keyCache,
                    callback: function () use ($with, $count) {
                        return $this->categoryRepository->query()
                            ->with($with)
                            ->filters()
                            ->whereNull('parentId')
                            ->take($count)
                            ->orderBy('categories.sort', 'DESC')
                            ->get();
                    },
                    tags: [],
                    ttl: $this->minutes,
                );

            }


        } else {


            $data = $this->cacheHelper->remember(
                key: $keyCache,
                callback: function () use ($with) {
                    return $this->categoryRepository->query()->orderBy('categories.sort', 'DESC')->getAll(["*"], $with);
                },
                tags: [],
                ttl: $this->minutes,
            );




        }

        return $this->sendSuccess(
            new OptionsResource(
                resource: $data,
                textColumn: $modelOptions['text_column'] ?? 'name',
                valueColumn: $modelOptions['value_column'] ?? 'id',
                meta: $modelOptions['meta'] ?? [],
                textSeparator: $modelOptions['text_separator'] ?? ''
            )
        );
    }



}