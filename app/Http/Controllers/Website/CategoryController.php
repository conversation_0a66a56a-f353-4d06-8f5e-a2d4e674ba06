<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\Request;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\CategoryResource;
use App\Repositories\Category\CategoryRepositoryInterface;

use Illuminate\Http\JsonResponse;

class CategoryController extends ApiBaseController
{
    /**
     * @param  CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(protected CategoryRepositoryInterface $categoryRepository)
    {
    }
    /**
     * Get all Categories .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $prams = request()->all();
        $stringQuery = '';
        if (count($prams)) {
            try {
                $stringQuery = implode('-', array_map(function ($key, $value) {
                    return $key . '-' . $value;
                }, array_keys($prams), $prams));
            } catch (\Throwable $th) {
                $stringQuery = '';
            }
        }
        $data = $this->categoryRepository->getAllCategoriesCached($stringQuery);
        return $this->sendSuccess(CategoryResource::generate($data));
    }

    /**
     * Show Category by slug .
     *
     * @param int|string $category
     * @return JsonResponse
     */
    public function show($category, $categoryLevel2 = null, $categoryLevel3 = null, $categoryLevel4 = null): JsonResponse
    {
        $data = $this->categoryRepository->findBySlugCategoryLevels($category, $categoryLevel2, $categoryLevel3, $categoryLevel4);
        return $this->sendSuccess(CategoryResource::generate($data));
    }

}