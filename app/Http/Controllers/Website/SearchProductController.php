<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\AutocompleteResource;
use App\Http\Resources\Website\SearchProductResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

use App\Models\Product;
use App\Repositories\Brand\BrandRepositoryInterface;
use App\Repositories\Category\CategoryRepositoryInterface;
use App\Repositories\Label\LabelRepositoryInterface;
use App\Repositories\SearchProduct\SearchRepositoryInterface;
use Meilisearch\Client;

class SearchProductController extends ApiBaseController
{

    public function __construct(protected SearchRepositoryInterface $searchRepository)
    {
    }
    public function search(Request $request): JsonResponse
    {

        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        $filters = $request->only($index->getFilterableAttributes());
        $filters = collect($filters)->filter()->all();
        $filtersGroupsId = 1;

        if ($request->has('categories') && $request->get('categories') !== null) {

            if (is_array($filters['categories'])) {
                $filters['categories'] = resolve(CategoryRepositoryInterface::class)->getCategoriesBySlugs($filters['categories'])->pluck('categoryId')->toArray();
            } elseif (is_string($filters['categories']) && !is_numeric($filters['categories'])) {

                $categoryRepository = app(CategoryRepositoryInterface::class);
                if ($request->has('slug') && $request->get(key: 'slug') != "") {

                    if ($request->has(key: 'slugs') && count($request->slugs) > 1) {
                        // need query for multiple slugs for 
                        $categories = $categoryRepository->checkCategoriesParents($request->slugs);

                        if ($categories->count() != count($request->slugs)) {

                            $categoryModel = $categoryRepository->query()->whereJsonContains('oldSlug->' . current_locale(), $request->slug)->first();


                            if ($categoryModel) {
                                // If found by oldSlug, construct the redirect URL
                                $redirectUrl = $categoryModel->slug;
                                $parent = $categoryModel->parent;
                                while ($parent) {
                                    $redirectUrl = $parent->slug . '/' . $redirectUrl;
                                    $parent = $parent?->parent;
                                }
                                throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
                            }

                            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                        }

                        $category = $categories->where('slug', '=', $request->slug)->first();


                        if (!$category) {
                            $categoryModel = $categoryRepository->query()->whereJsonContains('oldSlug->' . current_locale(), $request->slug)->first();
                            if ($categoryModel) {
                                // If found by oldSlug, construct the redirect URL
                                $redirectUrl = $categoryModel->slug;
                                $parent = $categoryModel->parent;
                                while ($parent) {
                                    $redirectUrl = $parent->slug . '/' . $redirectUrl;
                                    $parent = $parent?->parent;
                                }
                                throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
                            } else {
                                // If not found by either slug or oldSlug, throw the original exception
                                throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                            }
                        }

                    } else {

                        $category = $categoryRepository->query()->where('slug', '=', $request->slug)->whereNull('parentId')->first();
                        if (!$category) {
                            $categoryModel = $categoryRepository->query()->whereJsonContains('oldSlug->' . current_locale(), $request->slug)->first();
                            if ($categoryModel) {
                                // If found by oldSlug, construct the redirect URL
                                $redirectUrl = $categoryModel->slug;
                                $parent = $categoryModel->parent;
                                while ($parent) {
                                    $redirectUrl = $parent->slug . '/' . $redirectUrl;
                                    $parent = $parent?->parent;
                                }
                                throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
                            } else {
                                // If not found by either slug or oldSlug, throw the original exception
                                throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                            }
                        }
                    }
                } else {
                    $category = $categoryRepository->getCategoryBySlug($filters['categories']);
                }

                $filtersGroupsId = $category->filtersGroupsId ?? 1;
                $filters['categories'] = $category->categoryId ?? null;
            }
        }


        if ($request->has('brandId')) {
            if (is_array($filters['brandId'])) {
                $filters['brandId'] = resolve(BrandRepositoryInterface::class)->getBrandsBySlugs($filters['brandId'])->pluck('brandId')->toArray();
            } elseif (is_string($filters['brandId']) && !is_numeric($filters['brandId']) && !$request->has('slug')) {
                $brand = resolve(BrandRepositoryInterface::class)->getBrandBySlug($filters['brandId']);
                $filtersGroupsId = $brand->filtersGroupsId ?? 1;
                $filters['brandId'] = $brand->brandId ?? null;
            }
        }

        $data = $this->searchRepository->search(
            filters: $filters,
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
            orderBy: $request->orderBy ?? 'score,desc',
            query: $request->q ?? '',
            filtersGroupsId: $filtersGroupsId,
        );


        return $this->sendSuccess(SearchProductResource::generate($data, Product::class));
    }


    public function autocomplete(Request $request)
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        $filters = $request->only($index->getFilterableAttributes());
        if ($request->has('categories')) {
            $categoryRepository = resolve(CategoryRepositoryInterface::class);
            if (is_array($filters['categories'])) {
                $filters['categories'] = $categoryRepository->getCategoriesBySlugs($filters['categories'])->pluck('categoryId')->toArray();
            } elseif (is_string($filters['categories']) && !is_numeric($filters['categories'])) {
                $filters['categories'] = $categoryRepository->getCategoryBySlug($filters['categories'])->categoryId ?? 0;
            }
        }

        $data = $this->searchRepository->autocomplete(
            filters: $filters,
            limit: 26,
            orderBy: $request->orderBy ?? 'score,desc',
            query: $request->q ?? '',
        );

        return $this->sendSuccess(AutocompleteResource::generate($data));
    }

    public function products(Request $request): JsonResponse
    {

        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        $filters = $request->only($index->getFilterableAttributes());
        if ($request->has('categories')) {
            $categoryRepository = resolve(CategoryRepositoryInterface::class);
            if (is_array($filters['categories'])) {
                $filters['categories'] = $categoryRepository->getCategoriesBySlugs($filters['categories'])->pluck('categoryId')->toArray();
            } elseif (is_string($filters['categories']) && !is_numeric($filters['categories'])) {
                $filters['categories'] = $categoryRepository->getCategoryBySlug($filters['categories'])->categoryId ?? 0;
            }
        }

        $data = $this->searchRepository->products(
            filters: $filters,
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
            orderBy: $request->orderBy ?? 'score,desc',
            query: $request->q ?? '',
        );
        return $this->sendSuccess(SearchProductResource::generate($data));
    }

    public function mostPopular(Request $request): JsonResponse
    {
        $limit = $request->limit ?? 20;
        $data = $this->searchRepository->mostPopular((int) $limit);
        return $this->sendSuccess(SearchProductResource::generate($data), Product::class);
    }
    public function newArrival(Request $request)
    {
        $limit = $request->limit ?? 20;
        $data = $this->searchRepository->newArrival((int) $limit);
        return $this->sendSuccess(SearchProductResource::generate($data), Product::class);
    }




    public function homeOffers(Request $request): JsonResponse
    {
        $limit = $request->limit ?? 20;
        $data = $this->searchRepository->offers((int) $limit);
        return $this->sendSuccess(SearchProductResource::generate($data), Product::class);
    }


    public function offers(Request $request): JsonResponse
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        $filters = $request->only($index->getFilterableAttributes());

        $filters['hasOffer'] = true;
        $filtersGroupsId = 1;
        $data = $this->searchRepository->search(
            filters: $filters,
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
            orderBy: $request->orderBy ?? 'score,desc',
            query: $request->q ?? '',
            filtersGroupsId: $filtersGroupsId,
        );


        return $this->sendSuccess(SearchProductResource::generate($data, Product::class));
    }



    public function productsHistory(Request $request): JsonResponse
    {
        $limit = $request->limit ?? 20;
        $data = $this->searchRepository->productsVisit((int) $limit);
        return $this->sendSuccess(SearchProductResource::generate($data), Product::class);
    }




    public function suggestedProducts($productId, Request $request): JsonResponse
    {
        $limit = $request->limit ?? 4;
        $data = $this->searchRepository->suggestedProducts($productId, (int) $limit);
        return $this->sendSuccess(SearchProductResource::generate($data), Product::class);
    }



}