<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;

class ShippingCarriersRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name.ar' => 'required',
            'name.en' => 'required',
            'slug' => 'nullable',
            'phone' => 'required',
            'address' => 'nullable',
            'prices.*.price.currencyId' => 'integer|exists:currencies,currencyId',
            'prices.*.price.value' => 'numeric|min:0.1',
            'prices.*.cityId' => 'integer|exists:cities,cityId',


        ];
    }
}