<?php

namespace App\Http\Requests\Admin;

use App\Rules\ContainsJsonENAR;
use App\Rules\UniqueJson;
use App\Http\Requests\BaseFormRequest;
use App\Models\Category;

class CategoryRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'name' => ['required', new ContainsJsonENAR, new UniqueJson(Category::getTableName(), 'name', ['ar', 'en'], $this->route('category')),],
            'name' => ['required'],
            'parentId' => 'nullable|integer|exists:categories,categoryId',
            'categoriesAttributes' => 'nullable|exists:attributes,attributeId',
            'categoriesBrands' => 'nullable|exists:brands,brandId',
            'filtersGroupsId' => 'nullable|exists:filters_groups,filtersGroupsId',
            'sort' => 'required|integer',
            'media' => 'nullable',
            'priority' => 'integer',
            'metaDescription' => 'nullable',
            'metaTitle' => 'nullable',
            'isShowBrandIngListing' => 'required|boolean',
        ];
    }
}