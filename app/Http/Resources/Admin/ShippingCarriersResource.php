<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Admin\AddressResource;


class ShippingCarriersResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);


        return [
            "shippingCarrierId" => $this->shippingCarrierId,
            "name" => $this->name,
            "slug" => $this->slug,
            "phone" => $this->phone,
            "label" => $this->label,
            "haveFastShipping" => $this->haveFastShipping,
            "default" => $this->default,
            'createdAt' => $this->createdAt,
            'updatedAt' => $this->updatedAt,
            // 'addressId' => $this->addressId,
            // "address" => AddressResource::generate($this->whenLoaded('address')),
            "prices" => ShippingCarrierPriceCity::generate($this->whenLoaded('priceCities')),

        ];

    }
}