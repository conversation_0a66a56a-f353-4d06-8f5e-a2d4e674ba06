<?php

namespace App\Http\Resources\Website;

use App\Helper\Calculator;
use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;
use App\Http\Resources\General\PriceResource;


class ItemCartResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {


        $response = [
            "cartId" => $this->cartId,
            "varianceId" => $this->varianceId,
            "productId" => $this->productId,
            'bundleId' => $this->bundleId,
            "quantity" => $this->quantity,
            "name" => $this->product->name ?? '',
            "SKU" => $this->product->SKU ?? '',
            "type" => $this->product->type ?? '',
        ];

        if ($response['type'] == 'bundle') {

            $response['stock'] = $this->whenLoaded('product') ? new StockResource($this->product->activeStock) : null;

        } elseif ($response['type'] == 'simple') {
            $response['stock'] = StockResource::generate($this->whenLoaded('activeStock'));
        } else {
            $response['stock'] = StockResource::generate($this->whenLoaded('activeStock'));
        }

        $response['variance'] = ProductVarianceResource::generate($this->whenLoaded('variance'));


        $response['media'] = MediaResource::generate($this->product);
        return $response;

    }




}