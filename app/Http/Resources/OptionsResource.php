<?php

namespace App\Http\Resources;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class OptionsResource extends ApiJsonResource
{
    public function __construct(
        $resource,
        protected string|array $textColumn = '',
        protected string|array $valueColumn = '',
        protected array $meta = [],
        protected string $textSeparator = ' ',
    ) {
        parent::__construct($resource);
    }

    public function toArray($request): array
    {
        $resource = $this->resource instanceof Collection ? $this->resource : collect($this->resource);

        return $resource->map(fn($item) => [
            'text' => $this->getTextColumn($item),
            'value' => $this->getValueColumn($item),
            'meta' => $this->when(!blank($this->meta), function () use ($item) {
                $meta = [];
                foreach ($this->meta as $column) {
                    if (is_array($column)) {
                        $meta[key($column)] = get_media_url(head($column), $item->{key($column)});
                    } else {
                        if ($column === 'children') {
                            $meta[$column] = new OptionsResource(
                                resource: $item->{$column},
                                textColumn: $this->textColumn,
                                valueColumn: $this->valueColumn,
                                meta: $this->meta,
                                textSeparator: $this->textSeparator
                            );
                        } else {
                            $meta[$column] = $item->{$column};
                        }
                    }
                }

                return $meta;
            }),
        ])->toArray();
    }
    private function getValueColumn($item): object|string|int|array
    {
        if (is_array($this->valueColumn)) {
            $values = collect($this->valueColumn)->map(fn($i) => $this->getPropertyValue($item, $i))->toArray();

            $object = new \stdClass();

            foreach ($values as $key => $value) {
                $object->$key = $value;
            }

            return $object;
        }

        return $this->getPropertyValue($item, $this->valueColumn);
    }

    private function getTextColumn($item)
    {
        try {
            if (is_array($this->textColumn)) {
                return collect($this->textColumn)->map(fn($i) => $this->getPropertyValue($item, $i))->implode("$this->textSeparator");
            }
            return $this->getPropertyValue($item, $this->textColumn);
        } catch (\Exception $exception) {

            dd($exception);
        }

    }

    private function getPropertyValue($item, $property)
    {
        try {
            $properties = explode('.', $property);
            $value = $item;

            foreach ($properties as $prop) {
                try {
                    $value = $value->{$prop};
                } catch (\Throwable $th) {
                    dd($value, $prop, $property);
                }
            }

            return $value;
        } catch (\Exception $exception) {
            dd($exception);
        }
    }


}