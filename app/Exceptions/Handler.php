<?php

namespace App\Exceptions;

use App\Models\Visitor;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Session\TokenMismatchException;
use Sentry\Laravel\Integration;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Throwable;
use App\Traits\ApiResponser;
use Dotenv\Exception\ValidationException;
use Http\Client\Exception\HttpException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    use ApiResponser;
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            if ($e->getCode() == Response::HTTP_MOVED_PERMANENTLY) {
                return; // Ignore 301 status exceptions
            }
            if ($user = getUserCached()) {
                if ($user) {
                    if ($user->getMorphClass() === Visitor::class) {
                        Integration::configureScope(function (\Sentry\State\Scope $scope) use ($user) {
                            $scope->setUser([
                                'visitorId' => $user->visitorId,
                                'visitorKey' => $user->visitorKey,
                            ]);
                        });
                    } else {
                        Integration::configureScope(function (\Sentry\State\Scope $scope) use ($user) {
                            $scope->setUser([
                                'userId' => $user->userId,
                                'firstName' => $user->firstName,
                                'lastName' => $user->lastName,
                                'phone' => $user->phone,
                            ]);
                        });
                    }
                }

                // Integration::configureScope(function (\Sentry\State\Scope $scope) {
                //     $user = [];
                //     if (isVisitor()) {
                //         $user = [
                //             'visitorId' => auth(GUARD_API)->user()->visitorId,
                //             'visitorKey' => auth(GUARD_API)->user()->visitorKey,
                //         ];
                //     } else {
                //         $user = [
                //             'userId' => auth(GUARD_API)->user()->userId,
                //             'firstName' => auth(GUARD_API)->user()->firstName,
                //             'lastName' => auth(GUARD_API)->user()->lastName,
                //             'phone' => auth(GUARD_API)->user()->phone,
                //         ];
                //     }
                //     $scope->setUser($user);

                // });
            }


            if ($e->getCode() != Response::HTTP_LOCKED) {
                Integration::captureUnhandledException($e);
            }

        });
    }

    public function render($request, Throwable $exception)
    {

        $statusCode = 500;

        if ($exception instanceof \Exception && $exception->getCode() === Response::HTTP_LOCKED) {
            return $this->sendError([], $exception->getMessage(), Response::HTTP_LOCKED);
        }
        if ($exception instanceof \App\Exceptions\ModelNotFoundException) {
            $statusCode = $exception->getCode() ?: 404;
            return $this->sendError(['line' => $exception->getLine(), 'redirectUrl' => $exception->getRedirectUrl(), 'file' => $exception->getFile(), 'trace' => $exception->getTrace(),], $exception->getMessage(), $statusCode);
        } elseif ($exception instanceof ModelNotFoundException) {
            $statusCode = $exception->getCode() ?: 404;
        } elseif ($exception instanceof MethodNotAllowedHttpException || $exception instanceof NotFoundHttpException || $exception instanceof HttpException) {
            $statusCode = 405; // or 404, or any other appropriate status code
        } elseif ($exception instanceof ValidationException) {
            $statusCode = 422;
        } elseif ($exception instanceof AuthenticationException) {
            $statusCode = 403;
        } elseif ($exception instanceof AuthorizationException) {
            // Handle AuthorizationException
        } elseif ($exception instanceof QueryException) {
            // Handle QueryException, TaskFailedException, JobException
            $statusCode = 500;
        } elseif ($exception instanceof TokenMismatchException) {
            // Handle TokenMismatchException
            $statusCode = 419;
        } elseif ($exception instanceof FileNotFoundException) {
            // Handle FileNotFoundException
            $statusCode = 404;
        } elseif ($exception instanceof UnauthorizedException) {
            $statusCode = 403;
        } else {
            // Handle other exceptions
            $statusCode = 500;
        }

        return $this->sendError(['line' => $exception->getLine(), 'file' => $exception->getFile(), 'trace' => $exception->getTrace(),], $exception->getMessage(), $statusCode);


    }
}