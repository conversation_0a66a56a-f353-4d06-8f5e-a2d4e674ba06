<?php

namespace App\Services;

use Google\Client as Google_Client;
use Google\Service\Drive as Google_Service_Drive;
use Google\Service\Drive\DriveFile as Google_Service_Drive_DriveFile;

class GoogleDriveService
{
    protected Google_Service_Drive $drive;

    // Set this to your uploaded folder ID or leave null to upload to root
    protected ?string $parentFolderId = null;

    public function __construct()
    {
        $client = new Google_Client();
        $client->setAuthConfig(storage_path('client_secret_backup.json')); // Update path if needed
        $client->addScope(Google_Service_Drive::DRIVE_FILE); // Only allow file-level access

        $this->drive = new Google_Service_Drive($client);
    }

    /**
     * Uploads a file to Google Drive
     *
     * @param string $filePath Local file path
     * @param string $fileName Name to use on Google Drive
     * @return string|null Uploaded file ID or null on failure
     */
    public function upload(string $filePath, string $fileName): ?string
    {
        $fileMetadata = new Google_Service_Drive_DriveFile([
            'name' => $fileName,
            'parents' => $this->parentFolderId ? [$this->parentFolderId] : []
        ]);

        try {
            $content = file_get_contents($filePath);

            $file = $this->drive->files->create($fileMetadata, [
                'data' => $content,
                'mimeType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'uploadType' => 'multipart',
                'fields' => 'id'
            ]);

            return $file->id;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Optionally allow setting a parent folder ID
     */
    public function setParentFolder(string $folderId): void
    {
        $this->parentFolderId = $folderId;
    }

    /**
     * Create a folder and return its ID
     *
     * @param string $folderName
     * @param string|null $parentId
     * @return string|null
     */
    public function createFolder(string $folderName, ?string $parentId = null): ?string
    {
        $folderMetadata = new Google_Service_Drive_DriveFile([
            'name' => $folderName,
            'mimeType' => 'application/vnd.google-apps.folder',
        ]);

        if ($parentId) {
            $folderMetadata->setParents([$parentId]);
        }

        try {
            $folder = $this->drive->files->create($folderMetadata, [
                'fields' => 'id'
            ]);
            return $folder->id;
        } catch (\Exception $e) {
            logger()->error('Google Drive Folder Creation Error: ' . $e->getMessage());
            return null;
        }
    }
}