<?php

return [
    'title' => [
        'translations' => 'Translations',
        'products' => 'Products',
        'categories' => 'Categories',
        'product-group' => 'Product Group',
        'colors' => 'Colors',
        'attributes' => 'Attributes',
        'suppliers' => 'Suppliers',
        'ratings' => 'Ratings',
        'auctions' => 'Auctions',
        'currencies' => 'Currencies',
        'payments-methods' => 'Payments Methods',
        'cities' => 'Cities',
        'countries' => 'Countries',
        'shippings' => 'Shippings',
        'translations-tags' => 'Translations Tags',
        'system-configuration' => 'System Configuration',
        'brands' => 'Brands',
        'welcome' => 'Welcome',
        'action-used-souq' => 'Action-Used-Souq',
        'ai' => 'Artificial Intellgence',
        'profile' => 'Profile',
        'account' => 'My Account',
        'wishlist' => 'Wishlist',
        'my-ads' => 'My Ads',
        'orders' => 'Orders',
        'reviews' => 'Reviews',
        'addresses' => 'Addresses',
        'wallet' => 'Transactions',
        'transactions' => 'Transactions',
        'ads' => 'Ads',
        'shippings-carriers' => 'Shippings Carriers',
        'filters' => 'Filters',
        'users' => 'Users',
        'pages' => 'Pages',
        'contact-us' => 'Contact Us Page',
        'product-labels' => 'Product labels',
    ],
    'common' => [
        'key' => 'Key',
        'created-at' => 'Created At',
        'updated-at' => 'Updated At',
        'name' => 'Name',
        'content' => 'Content',
        'tags' => 'Tags',
        'action' => 'Action',
        'published' => 'Published',
        'category' => 'Category',
        'type' => 'Type',
        'listed' => 'Listed',
        'parent-category' => 'Parent Category',
        'media' => 'Image',
        'color' => 'Color',
        'review' => 'Review',
        'reviewer' => 'Reviewer',
        'rating' => 'Rating',
        'publish' => 'Publish',
        'bid-start' => 'Bid Starting Date',
        'start-time' => 'Start Time',
        'end-time' => 'End Time',
        'off-display-time' => 'Off Display Time',
        'currency' => 'Currency',
        'symbol' => 'Symbol',
        'value-to-base-price' => 'Value to Base Price',
        'status' => 'Status',
        'module' => 'Module',
        'phone' => 'Phone',
        'iso-code' => 'ISO Code',
        'phone-code' => 'Phone Code',
        'code-city' => 'Code City',
        'prefix' => 'Prefix',
        'suffix' => 'Suffix',
        'isrequired' => 'Is  Required',
        'search-type' => 'Search Type',
        'insert-type' => 'Insert Type',
        'display-type' => 'Display Type',
        'new-product' => 'New Product',
        'new-attribute' => 'New Attribute',
        'new-category' => 'New Category',
        'new-brand' => 'New Brand',
        'new-color' => 'New Color',
        'new-supplier' => 'New Supplier',
        'new-shipping' => 'New Shipping',
        'new-auction' => 'New Auction',
        'new-currency' => 'New Currency',
        'new-payments-methods' => 'New Payments Methods',
        'new-country' => 'New Country',
        'new-city' => 'New City',
        'shippings' => 'Shipping Carriers',
        'new-translation' => 'New Translation',
        'bulk-update' => 'Bulk Update',
        'new-product-group' => 'New Product Group',
        'general-information' => 'General Information',
        'related-and-suggested' => 'Related and Suggested',
        'add-variance' => 'Add Variance',
        'variances-attribute' => 'Variances Attribute',
        'variances-stock' => 'Variances Stock',
        'variances-media' => 'Variances Media',
        'add-new-stock' => 'New Stock',
        'attributes' => 'Attributes',
        'add' => 'Add',
        'cancel' => 'Cancel',
        'save' => 'Save',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'yes' => 'Yes',
        'no' => 'No',
        'product' => 'Product',
        'add-new' => 'Add New',
        'product-group' => 'Product Group',
        'brand' => 'Brand',
        'attribute' => 'Attribute',
        'translation' => 'Translation',
        'supplier' => 'Supplier',
        'auction' => 'Auction',
        'city' => 'City',
        'shipping' => 'Shipping Carrier',
        'payment-method' => 'Payment Method',
        'add-new-option' => 'Add New Option',
        'translation-tag' => 'Translation Tag',
        'new-translation-tag' => 'New Translation Tag',
        'description' => 'Description',
        'is-published' => 'Is Published',
        'is-listed' => 'Is Listed',
        'related-products' => 'Related Products',
        'suggested-products' => 'Suggested Products',
        'variance-name' => 'Variance Name',
        'children-name' => 'Children Name',
        'address' => 'Address',
        'is-required' => 'Is Required',
        'option' => 'Option',
        'country' => 'Country',
        'unpublish' => 'Unpublish',
        'show-on' => 'Show On',
        'html' => 'html',
        'key-type' => 'Key Type',
        'paste' => 'Paste',
        'allow-key-edit' => 'Allow Key Edit',
        'translations' => 'Translations',
        'there-is-no-placeholders-yet' => 'There Is No Placeholders Yet!',
        'group' => 'Group',
        'add-new-placeholder' => 'Add New Placeholder',
        'import' => 'Import',
        'export' => 'Export',
        'label' => 'Label',
        'value' => 'Value',
        'translate-from' => 'Translate From',
        'to-language' => 'To Language',
        'show-only-missing' => 'Show Only Missing',
        'translations-locale' => 'Translations Locale',
        'search' => 'Search',
        'sku' => 'Stock Number',
        'content-will-be-copied-automatically-on-save' => 'Content Will Be Copied Automatically On Save',
        'orders' => 'The Requests',
        'price' => 'Price',
        'new-used-category' => 'New Used Category',
        'used-category' => 'Used Category',
        'new-used-product' => 'New Used Product',
        'set-active' => 'Set Active',
        'users' => 'Users',
        'new-user' => 'New User',
        'user' => 'User',
        'gender' => 'Gender',
        'first-name' => 'First Name',
        'last-name' => 'Last Name',
        'password' => 'Password',
        'password-confirmation' => 'Password Confirmation',
        'request-change-name' => 'Request to Change Name',
        'deleted-users' => 'Deleted Users',
        'full-name' => 'Full Name',
        'new-page' => 'New Page',
        'shipping-carrier' => 'Shipping Carrier',
        'new-shipping-carrier' => 'New Shipping Carrier',
        'new-child-filter' => 'Filter',
        'update' => 'Update',
        'close' => 'Cancel',
        'title' => 'My Account',
        'addresses' => 'Addresses',
        'reviews' => 'Reviews',
        'please-select-the-gender' => 'Gender',
        'male' => 'Male',
        'female' => 'Female',
        'upload' => 'Upload',
        'post-ad-now' => 'Post Ad Now',
        'terms-and-policy' => 'Terms And Policy',
        'privacy-policy' => 'Privacy Policy',
        'services' => 'Services',
        'visa' => 'Visa',
        'madfooatcoma' => 'Madfooatcom',
        'payment-is-cash' => 'Cash Payments',
        'support' => 'Support',
        'customer-support' => 'Customer Support',
        'customers-service' => 'Customers Service',
        'follow-us' => 'Follow Us At',
        'bracnhes' => 'Branches',
        'connect-us' => 'Contact Us',
        'about-us' => 'About Us',
        'download-our-app' => 'Download App',
        'address-book' => 'Address Book',
        'default-address' => 'Default Address',
        'default' => 'Default',
        'add-new-address' => 'New Address',
        'edit-address' => 'Edit Address',
        'contact-information' => 'Contact Information',
        'save-address' => 'Save Address',
        'recipient-name' => 'Recipient Name',
        'recipient-phone' => 'Recipient Phone',
        'street' => 'Street',
        'district' => 'District',
        'apartment-number' => 'Apartment Number',
        'building-number' => 'Building Number',
        'profile-picture' => 'Profile Picture',
        'change-password' => 'Change Password',
        'upload-image' => 'Upload Image',
        'my-orders' => 'My Orders',
        'my-ads' => 'My Ads',
        'my-wishlist' => 'My Wishlist',
        'my-addresses' => 'My Address',
        'settings' => 'Settings',
        'logout' => 'Logout',
        'my-profile' => 'My Profile',
        'my-account' => 'My Account',
        'hello' => 'Welcome,',
        'my-dashboard' => 'My Dashboard',
        'payment-methods' => 'Payment Methods',
        'order-by' => 'Filter By',
        'date-from-recent-to-oldest' => 'From Recent to Oldest',
        'avg-desc-customer-review' => 'Lowest Customer Review',
        'date-from-oldest-to-recent' => 'From Oldest to Recent',
        'price-from-high-to-low' => 'Price From High to Low',
        'price-from-low-to-high' => 'Price From Low to High',
        'avg-asc-customer-review' => 'Highest Customer Review',
        'checkout' => 'Checkout',
        'code' => 'Country Code',
        'order-tracking' => 'Order Details',
        'order-recieved' => 'Order Received',
        'in-delivery' => 'In Delivery',
        'remembered-your-account' => 'Remember Your Account',
        'back' => 'Back',
        'customer-support-get-your-texts-emails-answered-in-your-native-language' => 'Our Support Team Is Available 24/7 to Ensure You Receive The Help You Need Whenever You Need It.',
        'copyright-2023-action-website-all-rights-reserved' => '© 2024 ActionJo. All Rights Reserved.',
        'branches' => 'Branches',
        'jod' => 'JD',
        'usd' => 'USD',
        'sar' => 'SAR',
        'what-are-you-looking' => 'Discover Our Range Of  Products',
        'used-products' => 'Used Products',
        'used-categories' => 'Used Categories',
        'parent-used-categories' => 'Parent Used Categories',
        'published-at' => 'Published At',
        'model' => 'Model',
        'prompt' => 'Prompt',
        'please-select-the-birthday' => 'Choose Your Date of Birth',
        'website-use-policy' => 'Website Use Policy',
        'terms-of-wallet' => 'Terms Of Wallet',
        'terms-of-service' => 'Terms Of Service',
        'your-name' => 'Name',
        'email' => 'Email',
        'problem-type' => 'Problem Type',
        'message' => 'Message',
        'submit-now' => 'Submit Now',
        'next' => 'Next',
        'you-dont-have-any-addresses-yet' => 'You Don\'t Have Any Address Yet',
        'home' => 'Home',
        'delivered' => 'Delivered',
        'my-order' => 'My Order',
        'order-price' => 'Price:',
        'order-quantity' => 'Quantity:',
        'order-details' => 'Order Details',
        'total' => 'Total',
        'amount' => 'Amount',
        'order-checkout' => 'Continue order',
        'my-cart' => 'My Cart',
        'continue-shopping' => 'Continue Shopping',
        'quantity' => 'Quantity:',
        'categories' => 'Categories',
        'sign-in-register' => 'Sign In / Register',
        'contact' => 'Contact',
        'admin-dashboard' => 'Admin Dashboard',
        'review-submit-on-product-success' => 'Review Submit On Product Success',
        'number-not-matching-country' => 'Number Not Matching Country',
        'has-been-updated-successfully' => 'Has Been Updated Successfully',
        'active' => 'Active',
        'suspended' => 'Suspended',
        'customers' => 'Customers',
        'user-has-been-restore-successfully' => 'User Has Been Restore Successfully',
        'new-arrival' => 'New Arrival',
        'most-ordered' => 'Most Ordered',
        'restore' => 'Restore',
        'birthday' => 'Birthday',
        'the-name-will-be-changed-upon-approval-of-the-application-by-the-manager' => 'The Name Will Be Changed When The Application Is Approved By The Director',
        'item-has-been-created' => 'The Item Has Been Created',
        'unpaid' => 'Unpaid',
        'start-processing' => 'Start Processing',
        'note' => 'Note',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'all' => 'All',
        'payment-status' => 'Payment Status',
        'order' => 'Order',
        'paid' => 'Paid',
        'add-product' => 'Add Product',
        'remove' => 'Remove',
        'inactive' => 'Inactive',
        'payment' => 'Payment',
        'simple' => 'Simple',
        'alternative' => 'Alternative',
        'bundle' => 'Bundle',
        'partial-paid' => 'Partial Paid',
        'received' => 'Received',
        'incomplete-order' => 'Incomplete Order',
        'unknown' => 'Unknown',
        'text-field' => 'Text Field',
        'number-field' => 'Number Field',
        'checkbox' => 'Checkbox',
        'brands' => 'Brands',
        'canceled' => 'Cancelled',
        'shipped-by' => 'Type of Delivery',
        'your-cart-is-empty' => 'Your Cart is Empty',
        'page' => 'Page',
        'actions' => 'Actions',
        'subtotal' => 'Subtotal',
        'shipping-price' => 'Shipping Price',
        'old-password' => 'Old Password',
        'type-option' => 'Type Option',
        'options-list' => 'Options List',
        'by-group' => 'Group By',
        'publish-status' => 'Publish Status',
        'something-went-wrong' => 'Something Went Wrong - Try Again Please',
        'meta-title' => 'Meta Title',
        'meta-description' => 'Meta Description',
        'filters-groups' => 'Filters Group',
        'unpublish-at' => 'Unpublish At',
        'clear' => 'Clear',
        'ok' => 'Edit',
        'wallet' => 'Wallet',
        'offers' => 'Latest Offers',
        'see-more' => 'See More',
        'days' => 'Days',
        'and' => '&',
        'for-refund' => 'For Refund',
        'refunded' => 'Refunded',
        'drift' => 'Draft',
        'products' => 'Products',
        'draft' => 'Draft',
        'number' => 'Number',
        'blog' => 'Blog',
        'new-label' => 'New label',
        'slug' => 'slug',
        'start-from' => 'Start From',
    ],
    'menu' => [
        'dashboard' => 'Dashboard',
        'products' => 'Products',
        'inventory' => 'Inventory',
        'categories' => 'Categories',
        'product-group' => 'Product Group',
        'suppliers' => 'Suppliers',
        'ratings' => 'Ratings',
        'auctions' => 'Auctions',
        'brands' => 'Brands',
        'colors' => 'Colors',
        'attributes' => 'Attributes',
        'system-configurations' => 'System Configurations',
        'currencies' => 'Currencies',
        'payments-methods' => 'Payments Methods',
        'cities' => 'Cities',
        'countries' => 'Countries',
        'shippings' => 'Shippings',
        'translation' => 'Translations',
        'translation-tags' => 'Translation Tags',
        'help-support' => 'Help Support',
        'support' => 'Support',
        'use-ctrl-s-to-search' => 'Use Ctrl S to Search',
        'contact-us' => 'Contact Us',
        'pages' => 'Pages',
        'ai' => 'Artificial Intelligence',
        'request-change-name' => 'Request to Change Name',
        'deleted-users' => 'Deleted Users',
        'translations' => 'Translations',
        'orders' => 'Orders',
        'users' => 'Users',
        'action-used-souq' => 'Used Souq',
        'customers' => 'Customers',
        'categories-and-filters' => 'Categories & Filters',
        'banners' => 'Banners',
    ],
    'messages' => [
        'confirm-delete-text' => 'Are You Sure You Want To Delete?',
        'confirm-delete-title' => 'Confirm Deletion',
        'has-been-created-successfully' => 'Has Been Created Successfully',
        'has-been-deleted-successfully' => 'Has Been Deleted Successfully',
        'has-been-updated-successfully' => 'Has Been Updated Successfully',
    ],
    'errors' => [
        'field-required-empty' => 'Field Is Required',
        'number-not-matching-country' => 'Number Not Matching Country',
        'field-not-valid' => 'Field Not Valid',
    ],
    'ratings' => [
        'rating' => 'Rating',
        'review' => 'Reviews',
        'product-name' => 'Product Name',
        'status' => 'Status',
        'product-number' => '#',
    ],
    'auth' => [
        'login' => 'Login',
        'phone' => 'Phone',
        'password' => 'Password',
        'forgot-your-password' => 'Forgot Your Password',
        'or-login-using' => 'Or Login With',
        'you-don-t-have-an-account' => 'Don\'t Have An Account?',
        'create-account' => 'Create Account',
        'terms-of-use' => 'Terms Of Use',
        'privacy-policy' => 'Privacy Policy',
        'first-name' => 'First Name',
        'last-name' => 'Last Name',
        'email' => 'E-Mail',
        'mobile-number' => 'Mobile Number',
        'reg-confirmations' => 'Conditions of Use and Privacy Notice Confirmation',
        'forgot-password' => 'Account Recovery Options',
        'forgot-password-email' => 'Reset Password via Email',
        'remembered-your-password' => 'Remembered your Password',
        'reset-my-password' => 'Reset My Password',
        'forgot-password-phone' => 'Reset Password Via Phone',
        'register-account' => 'RegisterAccount',
        'forget-this' => 'Delete',
        'confirm-password' => 'Confirm Password',
        'otp-verification-code' => 'Please Enter the verification code sent to your email or phone number. This code ensures secure access to reset your password.',
        'change-your-password' => 'Change Password',
        'verify-email-now' => 'Verify',
        'verify-phone-now' => 'Verify',
        'forgot-email-password' => 'Update Password Using Email',
        'forgot-phone-password' => 'Update Password Using Phone Number',
        'forgot-email-password-description' => 'Please enter the email address associated with your account. We\'ll send password reset instructions to this email.',
        'forgot-phone-password-description' => 'Please Enter the phone number associated with your account. We\'ll send password reset instructions to this number.',
        'forgot-password-description' => 'Trouble logging in? No Worries! Enter your email or phone number, And we\'ll guide you through resetting your password.',
        'forgot-email-description' => 'Reset via Email',
        'forgot-phone-description' => 'Reset Via Phone',
        'otp-verification-code-profile' => 'We Sent A Verification Code to',
        'verify-your-email' => 'Verify',
        'didnt-receive-the-code' => 'Didn\'t Receive The Code?',
        'resend' => 'Resend',
        'verify-your-phone-number' => 'Verify',
        'remembered-your-account' => 'Remember Your Account',
        'have-an-account' => 'Have An Account',
    ],
    'login' => [
        'login-with-different-account' => 'Login With A Different Account',
    ],
    'header' => [
        'back-to-website' => 'Go to Website',
        'logout' => 'Logout',
        'change-password' => 'Change Password',
        'profile' => 'My Profile',
    ],
    'order' => [
        'payment' => 'Payment',
        'shipping' => 'Shipping',
        'contact-info' => 'Contact Info',
        'delivery' => 'Delivery',
        'subtotal' => 'Subtotal',
        'total' => 'Total',
        'building' => 'Building',
        'apartment' => 'Apartment',
        'there-is-no-any-payment-method-available-for-this-order' => 'There Is No Any Payment Method Available For This Order',
        'seconds' => 'Seconds',
        'you-will-be-redirected-to-your-orders-page-in' => 'You Will Be Redirected To Your Orders Page In',
        'pay-by-wallet' => 'Pay By Wallet',
        'you-need-to-login-to-use-your-wallet' => 'You Need To Login To Use Your Wallet',
        'remaining-balance' => 'Remaining Balance:',
        'your-order-amount' => 'Your Order Amount:',
        'your-current-wallet-balance-is' => 'Your Current Wallet Balance Is:',
        'pay-on-delivery' => 'Pay On Delivery',
        'pay-by-credit-card' => 'Pay By Credit Card',
        'credit-cards' => 'Credit Cards',
        'efawateercom' => 'eFAWATEERcom',
        'arab-bank' => 'Arab Bank',
        'pay-by-cash' => 'Complete Order',
        'order' => 'Order',
        'address' => 'Delivery to',
        'received' => 'Received',
        'editing' => 'Editing',
        'shipping-status' => 'Shipping Status',
        'order-info' => 'Order Info',
        'order-id' => 'Order ID',
        'has-profile' => 'Has Profile',
        'has-ordered-before' => 'Has Ordered Before',
        'order-items' => 'Order Items',
        'payment-invoices' => 'Payment Invoices',
        'invoice-id' => 'Invoice ID',
        'version-number' => 'Version Number',
        'order-delivery-id' => 'Delivery ID',
        'cancel-order' => 'Cancel Order',
        'end-edit' => 'End Edit',
        'start-processing' => 'Start Processing',
        'deliveries' => 'Delivery',
        'edit-quantity' => 'Edit Quantity',
        'info' => 'Information',
        'items' => 'Items',
        'add-product' => 'Add Product',
        'start-edit' => 'Start Edit',
        'set-as-delivered' => 'Set As Delivered',
        'there-is-no-any-notes-yet' => 'There Is No Any Notes Yet',
        'add-note' => 'Add Note',
        'user-id' => 'User ID',
        'delivery-to' => 'Delivery City to',
        'visitor-id' => 'Visitor ID',
        'contact' => 'Contact Information',
        'shipped-by' => 'Shipping Type',
        'number-of-orders' => 'Ordered Before',
        'thank-you-purchase-message' => 'Thank you for your purchase!',
        'my-orders' => 'My Orders',
        'for-refund' => 'Refund Process',
        'dear-customers' => 'Dear Customers',
        'message-shipping-body' => 'Please be informed that due to the Eid holiday, there will be a delay in order delivery  until 4th of April 2025. \nThank you for your understanding, and we wish you a happy Eid.',
        'shipping-message-shipping-footer' => 'Sincerely,\nAction Group',
    ],
    'listing' => [
        'search-results' => 'You\'re in Luck! Products Found. Explore Your Options',
    ],
    'product' => [
        'product-sku' => 'SKU / Model Number:',
        'write-a-review' => 'Write a Review',
        'availability-in-stock' => 'Available in Stock',
        'add-to-my-cart' => 'Add to Cart',
        'buy-now' => 'Buy Now',
        'quantity' => 'Quantity',
        'specification' => 'Specifications',
        'description' => 'Description',
        'customer-ratings' => 'Customer Ratings',
        'rate-out-of' => 'Out Of',
        'reviews' => 'Reviews',
        'write-your-review' => 'Write Your Review',
        'name' => 'Name',
        'your-review' => 'Your Review',
        'submit-review' => 'Submit Review',
        'save-to-my-wishlist' => 'Save to My Wishlist',
        'this-item-is-out-of-stock' => 'Product is Out of Stock',
        'added-to-my-wishlist' => 'Added to My Wishlist',
        'product-added-to-cart' => 'Product Added To Cart',
        'you-have-successfully-added-the-product-to-your-cart' => 'You Have Successfully Added The Product To Your Cart',
        'continue-shopping' => 'Continue Shopping',
        'view-cart' => 'View Cart',
        'out-of-stock' => 'Out Of Stock',
        'remove-item' => 'Remove Item',
        'are-you-sure-you-want-to-remove-this-item-from-your-cart' => 'Are you sure you want to remove this item from your cart?',
        'new-product' => 'New Product',
        'title' => 'Title',
        'desc' => 'Description',
        'general' => 'General',
        'category-and-attributes' => 'Category And Attributes',
        'variances' => 'Variances',
        'stocks-and-prices' => 'Stocks And Prices',
        'payment-and-shipping' => 'Payment And Shipping',
        'no-description' => 'No Description',
        'seo' => 'SEO',
        'was' => 'Before',
        'now' => 'Now',
        'save' => 'Savings Value',
        'pre-order' => 'Pre Order',
        'delete-stock' => 'Delete Stock for Selected Products',
        'delete-all-images' => 'Delete All Images',
        'delete-images-covor' => 'Delete Images Cover',
        'delete-images-gallery' => 'Delete Gallery Images',
        'add-variance' => 'Add A New Variance',
        'new-variance' => 'New Variance',
        'bundle' => 'Bundle',
        'flush' => 'Flush',
    ],
    'rules' => [
        'phone-example' => '*********',
    ],
    'vc' => [
        'password-requirements' => 'Password Requirements',
        'lowercase' => 'Lowercase',
        'uppercase' => 'Uppercase',
        'number' => 'Number',
        'special-char' => 'Special Character',
        'min-number-of-char' => 'Min Number Of Characters',
    ],
    'users' => [
        'full-name' => 'Full Name',
    ],
    'layout' => [
        'sign-in-register' => 'Register \\ Login',
        'cart' => 'Cart',
        'account' => 'Account',
        'search' => 'Search',
        'home' => 'Home',
    ],
    'search' => [
        'all' => 'All',
        'no-result' => 'There is no results matching your search "{searchText}".',
        'reduce-search-text' => 'Please reduce your search text and try again',
        'clear' => 'Clear',
        'start-typing' => 'Start typing to search',
    ],
];